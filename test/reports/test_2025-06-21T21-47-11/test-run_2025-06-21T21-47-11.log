[2025-06-21T21:47:11.043Z] ================================================================================
[2025-06-21T21:47:11.043Z] Test Run Started
[2025-06-21T21:47:11.043Z] Timestamp: 2025-06-21T21:47:11.043Z
[2025-06-21T21:47:11.043Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T21:47:11.043Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:47:11.043Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:47:11.043Z] ================================================================================
[2025-06-21T21:47:11.047Z] 
[RUN START] - Test execution beginning
[2025-06-21T21:47:11.047Z] Total Test Suites: 1
[2025-06-21T21:47:11.047Z] Test Environment: test
[2025-06-21T21:47:11.056Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:47:11.056Z] Suite Display Name: UNIT
[2025-06-21T21:47:11.736Z] 
  [CASE START] - TC-24783ED: should capture console.log output
[2025-06-21T21:47:11.736Z]   Module: log
[2025-06-21T21:47:11.736Z]   Full Path: Log Capture Test › should capture console.log output
[2025-06-21T21:47:11.739Z]   [Arrange] - Precondition: Test environment prepared for "should capture console.log output"
[2025-06-21T21:47:11.739Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:47:11.740Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:47:11.740Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:47:11.740Z]   [Act] - Step: Executing test logic for "should capture console.log output"
[2025-06-21T21:47:11.740Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:47:11.740Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:47:11.740Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:47:11.740Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:47:11.740Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T21:47:11.740Z]   [Act Log] - console: 2 outputs
[2025-06-21T21:47:11.740Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:47:11.740Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:47:11.740Z]   [Act Log] - +28ms [console.log] Console log level: info
[2025-06-21T21:47:11.740Z]   [Act Log] - +28ms [process.stdout] Console log level: info

[2025-06-21T21:47:11.740Z]   [Act Log] - +30ms [console.log] File log level: warn
[2025-06-21T21:47:11.740Z]   [Act Log] - +30ms [process.stdout] File log level: warn

[2025-06-21T21:47:11.740Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:47:11.740Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:47:11.740Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:47:11.740Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:47:11.740Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:47:11.740Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:47:11.740Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:47:11.740Z]   [CASE END] - Duration: 34ms
[2025-06-21T21:47:11.740Z] 
  [CASE START] - TC-4ABC9FD2: should capture winston logger output
[2025-06-21T21:47:11.740Z]   Module: log
[2025-06-21T21:47:11.740Z]   Full Path: Log Capture Test › should capture winston logger output
[2025-06-21T21:47:11.746Z]   [Arrange] - Precondition: Test environment prepared for "should capture winston logger output"
[2025-06-21T21:47:11.746Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:47:11.746Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:47:11.746Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:47:11.746Z]   [Act] - Step: Executing test logic for "should capture winston logger output"
[2025-06-21T21:47:11.746Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:47:11.746Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:47:11.746Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:47:11.746Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:47:11.746Z]   [Act Log] - Captured 3 program outputs during execution
[2025-06-21T21:47:11.746Z]   [Act Log] - process: 3 outputs
[2025-06-21T21:47:11.746Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:47:11.746Z]   [Act Log] - +3ms [process.stdout] 2025-06-21 22:47:11:4711 [32minfo[39m: [32mThis is a test winston info message[39m

[2025-06-21T21:47:11.746Z]   [Act Log] - +4ms [process.stdout] 2025-06-21 22:47:11:4711 [33mwarn[39m: [33mThis is a test winston warn message[39m

[2025-06-21T21:47:11.746Z]   [Act Log] - +5ms [process.stdout] 2025-06-21 22:47:11:4711 [31merror[39m: [31mThis is a test winston error message[39m

[2025-06-21T21:47:11.746Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:47:11.746Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:47:11.746Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:47:11.746Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:47:11.746Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:47:11.746Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:47:11.746Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:47:11.746Z]   [CASE END] - Duration: 5ms
[2025-06-21T21:47:11.746Z] 
  [CASE START] - TC-65E5315B: should capture mixed console and winston output
[2025-06-21T21:47:11.746Z]   Module: log
[2025-06-21T21:47:11.746Z]   Full Path: Log Capture Test › should capture mixed console and winston output
[2025-06-21T21:47:11.748Z]   [Arrange] - Precondition: Test environment prepared for "should capture mixed console and winston output"
[2025-06-21T21:47:11.748Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:47:11.748Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:47:11.748Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:47:11.748Z]   [Act] - Step: Executing test logic for "should capture mixed console and winston output"
[2025-06-21T21:47:11.748Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:47:11.748Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:47:11.748Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:47:11.748Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:47:11.748Z]   [Act Log] - Captured 2 program outputs during execution
[2025-06-21T21:47:11.748Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:47:11.748Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:47:11.748Z]   [Act Log] - +1ms [process.stdout] 2025-06-21 22:47:11:4711 [32minfo[39m: [32mWinston: Test initialization complete[39m

[2025-06-21T21:47:11.748Z]   [Act Log] - +1ms [process.stdout] 2025-06-21 22:47:11:4711 [32minfo[39m: [32mWinston: Test completed successfully[39m

[2025-06-21T21:47:11.748Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:47:11.748Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:47:11.748Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:47:11.748Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:47:11.748Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:47:11.748Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:47:11.748Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:47:11.748Z]   [CASE END] - Duration: 1ms
[2025-06-21T21:47:11.748Z] 
  [CASE START] - TC-8B4356C: should capture error scenarios
[2025-06-21T21:47:11.748Z]   Module: log
[2025-06-21T21:47:11.748Z]   Full Path: Log Capture Test › should capture error scenarios
[2025-06-21T21:47:11.749Z]   [Arrange] - Precondition: Test environment prepared for "should capture error scenarios"
[2025-06-21T21:47:11.750Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:47:11.750Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:47:11.750Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:47:11.750Z]   [Act] - Step: Executing test logic for "should capture error scenarios"
[2025-06-21T21:47:11.750Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:47:11.750Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:47:11.750Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:47:11.750Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:47:11.750Z]   [Act Log] - Captured 2 program outputs during execution
[2025-06-21T21:47:11.750Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:47:11.750Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:47:11.750Z]   [Act Log] - +1ms [process.stdout] 2025-06-21 22:47:11:4711 [33mwarn[39m: [33mWinston: Warning - risky operation detected[39m

[2025-06-21T21:47:11.750Z]   [Act Log] - +1ms [process.stdout] 2025-06-21 22:47:11:4711 [32minfo[39m: [32mWinston: Operation completed without errors[39m

[2025-06-21T21:47:11.750Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:47:11.750Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:47:11.750Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:47:11.750Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:47:11.750Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:47:11.750Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:47:11.750Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:47:11.750Z]   [CASE END] - Duration: 1ms
[2025-06-21T21:47:11.755Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:47:11.755Z] Suite Duration: 677ms
[2025-06-21T21:47:11.755Z] Suite Results: 4 passed, 0 failed, 0 skipped
[2025-06-21T21:47:11.761Z] 
================================================================================
[2025-06-21T21:47:11.761Z] Test Run Finished
[2025-06-21T21:47:11.761Z] End Timestamp: 2025-06-21T21:47:11.761Z
[2025-06-21T21:47:11.761Z] Total Duration: 718ms (0.72s)
[2025-06-21T21:47:11.761Z] 
[STATISTICS]
[2025-06-21T21:47:11.761Z] Total Test Suites: 1
[2025-06-21T21:47:11.761Z] Passed Test Suites: 1
[2025-06-21T21:47:11.761Z] Failed Test Suites: 0
[2025-06-21T21:47:11.761Z] Total Tests: 4
[2025-06-21T21:47:11.761Z] Passed Tests: 4
[2025-06-21T21:47:11.761Z] Failed Tests: 0
[2025-06-21T21:47:11.761Z] Skipped Tests: 0
[2025-06-21T21:47:11.761Z] Success Rate: 100.00%
[2025-06-21T21:47:11.761Z] Overall Result: FAILURE
[2025-06-21T21:47:11.761Z] ================================================================================
