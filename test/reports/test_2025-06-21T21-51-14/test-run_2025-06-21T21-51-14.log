[2025-06-21T21:51:14.651Z] ================================================================================
[2025-06-21T21:51:14.651Z] Test Run Started
[2025-06-21T21:51:14.651Z] Timestamp: 2025-06-21T21:51:14.651Z
[2025-06-21T21:51:14.652Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T21:51:14.652Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:51:14.652Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:51:14.652Z] ================================================================================
[2025-06-21T21:51:14.655Z] 
[RUN START] - Test execution beginning
[2025-06-21T21:51:14.655Z] Total Test Suites: 1
[2025-06-21T21:51:14.655Z] Test Environment: test
[2025-06-21T21:51:14.664Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/debug-log-capture.test.js
[2025-06-21T21:51:14.664Z] Suite Display Name: UNIT
[2025-06-21T21:51:15.222Z] 
  [CASE START] - TC-66011EA6: should debug log capture mechanism
[2025-06-21T21:51:15.222Z]   Module: debug
[2025-06-21T21:51:15.222Z]   Full Path: Debug Log Capture › should debug log capture mechanism
[2025-06-21T21:51:15.246Z]   [Arrange] - Precondition: Test environment prepared for "should debug log capture mechanism"
[2025-06-21T21:51:15.246Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:51:15.246Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:51:15.246Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:51:15.246Z]   [Act] - Step: Executing test logic for "should debug log capture mechanism"
[2025-06-21T21:51:15.246Z]   [Act Log] - Loading target module: debug-log-capture.js
[2025-06-21T21:51:15.246Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:51:15.246Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:51:15.246Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:51:15.246Z]   [Act Log] - Captured 5 program outputs during execution
[2025-06-21T21:51:15.246Z]   [Act Log] - console: 2 outputs
[2025-06-21T21:51:15.246Z]   [Act Log] - process: 3 outputs
[2025-06-21T21:51:15.246Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:51:15.246Z]   [Act Log] - +38ms [console.log] Console log level: info
[2025-06-21T21:51:15.246Z]   [Act Log] - +38ms [process.stdout] Console log level: info

[2025-06-21T21:51:15.246Z]   [Act Log] - +40ms [console.log] File log level: warn
[2025-06-21T21:51:15.246Z]   [Act Log] - +40ms [process.stdout] File log level: warn

[2025-06-21T21:51:15.247Z]   [Act Log] - +60ms [process.stdout] 2025-06-21 22:51:15:5115 [32minfo[39m: [32mThis winston log should be captured![39m

[2025-06-21T21:51:15.247Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:51:15.247Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:51:15.247Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:51:15.247Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:51:15.247Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:51:15.247Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:51:15.247Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:51:15.247Z]   [CASE END] - Duration: 64ms
[2025-06-21T21:51:15.251Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/debug-log-capture.test.js
[2025-06-21T21:51:15.252Z] Suite Duration: 565ms
[2025-06-21T21:51:15.252Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T21:51:15.256Z] 
================================================================================
[2025-06-21T21:51:15.256Z] Test Run Finished
[2025-06-21T21:51:15.256Z] End Timestamp: 2025-06-21T21:51:15.256Z
[2025-06-21T21:51:15.256Z] Total Duration: 605ms (0.60s)
[2025-06-21T21:51:15.256Z] 
[STATISTICS]
[2025-06-21T21:51:15.256Z] Total Test Suites: 1
[2025-06-21T21:51:15.256Z] Passed Test Suites: 1
[2025-06-21T21:51:15.256Z] Failed Test Suites: 0
[2025-06-21T21:51:15.256Z] Total Tests: 1
[2025-06-21T21:51:15.256Z] Passed Tests: 1
[2025-06-21T21:51:15.256Z] Failed Tests: 0
[2025-06-21T21:51:15.256Z] Skipped Tests: 0
[2025-06-21T21:51:15.256Z] Success Rate: 100.00%
[2025-06-21T21:51:15.256Z] Overall Result: FAILURE
[2025-06-21T21:51:15.256Z] ================================================================================
