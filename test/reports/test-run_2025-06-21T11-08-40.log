[2025-06-21T11:08:40.322Z] ================================================================================
[2025-06-21T11:08:40.322Z] Test Run Started
[2025-06-21T11:08:40.322Z] Timestamp: 2025-06-21T11:08:40.322Z
[2025-06-21T11:08:40.322Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T11:08:40.323Z] Runtime Version: Node.js v18.20.5
[2025-06-21T11:08:40.323Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T11:08:40.323Z] ================================================================================
[2025-06-21T11:08:40.325Z] 
[RUN START] - Test execution beginning
[2025-06-21T11:08:40.325Z] Total Test Suites: 14
[2025-06-21T11:08:40.325Z] Test Environment: test
[2025-06-21T11:08:40.331Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T11:08:40.331Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:42.605Z] 
  [CASE START] - TC-674469B3: should process incoming message and create session
[2025-06-21T11:08:42.605Z]   Module: whatsapp
[2025-06-21T11:08:42.605Z]   Full Path: WhatsApp Webhook Integration › should process incoming message and create session
[2025-06-21T11:08:42.605Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:42.605Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:42.605Z] 
  [CASE START] - TC-432D970F: should reject webhook with invalid signature
[2025-06-21T11:08:42.605Z]   Module: whatsapp
[2025-06-21T11:08:42.605Z]   Full Path: WhatsApp Webhook Integration › should reject webhook with invalid signature
[2025-06-21T11:08:42.605Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:42.605Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:42.605Z] 
  [CASE START] - TC-3376EAD8: should handle malformed webhook payload
[2025-06-21T11:08:42.605Z]   Module: whatsapp
[2025-06-21T11:08:42.605Z]   Full Path: WhatsApp Webhook Integration › should handle malformed webhook payload
[2025-06-21T11:08:42.605Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:42.605Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:42.605Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/whatsapp/webhook.test.js
[2025-06-21T11:08:42.605Z] Suite Duration: 2259ms
[2025-06-21T11:08:42.606Z] Suite Results: 0 passed, 0 failed, 3 skipped
[2025-06-21T11:08:42.606Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T11:08:42.606Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:42.745Z] 
  [CASE START] - TC-78A8B433: should verify refundStatus default value is NONE
[2025-06-21T11:08:42.746Z]   Module: refund
[2025-06-21T11:08:42.746Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refundStatus default value is NONE
[2025-06-21T11:08:42.746Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:42.746Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:42.746Z] 
  [CASE START] - TC-74F28778: should verify refund status enum values
[2025-06-21T11:08:42.746Z]   Module: refund
[2025-06-21T11:08:42.746Z]   Full Path: Refund System Integration Tests › refundStatus Default Value Tests › should verify refund status enum values
[2025-06-21T11:08:42.746Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:42.746Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:42.746Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/refund.integration.test.js
[2025-06-21T11:08:42.746Z] Suite Duration: 135ms
[2025-06-21T11:08:42.746Z] Suite Results: 0 passed, 0 failed, 2 skipped
[2025-06-21T11:08:42.746Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T11:08:42.746Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:43.799Z] 
  [CASE START] - TC-2E236918: should create real payment intent with Stripe
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should create real payment intent with Stripe
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-18AE2E8F: should retrieve real payment intent status
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should retrieve real payment intent status
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-6AADE1B7: should handle payment with test card
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle payment with test card
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-54B75689: should handle declined card
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Payment Intent Flow › should handle declined card
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-332A1C6D: should create real Stripe customer
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should create real Stripe customer
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-164683AD: should retrieve real Stripe customer
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Customer Management › should retrieve real Stripe customer
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-5FD44F7E: should process real webhook signature validation
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Real Webhook Processing › should process real webhook signature validation
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-74FBC87B: should handle invalid amount
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid amount
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] 
  [CASE START] - TC-56B8BFFE: should handle invalid currency
[2025-06-21T11:08:43.800Z]   Module: real
[2025-06-21T11:08:43.800Z]   Full Path: Real Stripe Payment Integration Tests › Error Handling with Real API › should handle invalid currency
[2025-06-21T11:08:43.800Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:43.800Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:43.800Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.real.test.js
[2025-06-21T11:08:43.800Z] Suite Duration: 1050ms
[2025-06-21T11:08:43.800Z] Suite Results: 0 passed, 0 failed, 9 skipped
[2025-06-21T11:08:43.801Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T11:08:43.801Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:44.574Z] 
  [CASE START] - TC-3CAE5E97: should create a new order
[2025-06-21T11:08:44.574Z]   Module: graphql
[2025-06-21T11:08:44.574Z]   Full Path: Order GraphQL Mutations › should create a new order
[2025-06-21T11:08:44.574Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:44.574Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:44.574Z] 
  [CASE START] - TC-6385F57E: should fail to create order with invalid restaurant ID
[2025-06-21T11:08:44.574Z]   Module: graphql
[2025-06-21T11:08:44.574Z]   Full Path: Order GraphQL Mutations › should fail to create order with invalid restaurant ID
[2025-06-21T11:08:44.574Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:44.574Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:44.574Z] 
  [CASE START] - TC-7D51EFC9: should fail to create order without authentication
[2025-06-21T11:08:44.574Z]   Module: graphql
[2025-06-21T11:08:44.574Z]   Full Path: Order GraphQL Mutations › should fail to create order without authentication
[2025-06-21T11:08:44.574Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:44.574Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:44.574Z] 
  [CASE START] - TC-7912DF71: should update order status
[2025-06-21T11:08:44.574Z]   Module: graphql
[2025-06-21T11:08:44.574Z]   Full Path: Order GraphQL Mutations › should update order status
[2025-06-21T11:08:44.574Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:44.574Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:44.574Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/orderMutations.test.js
[2025-06-21T11:08:44.574Z] Suite Duration: 770ms
[2025-06-21T11:08:44.574Z] Suite Results: 0 passed, 0 failed, 4 skipped
[2025-06-21T11:08:44.575Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T11:08:44.575Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:45.928Z] 
  [CASE START] - TC-3FC374DB: should have Order type in schema
[2025-06-21T11:08:45.928Z]   Module: graphql
[2025-06-21T11:08:45.928Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have Order type in schema
[2025-06-21T11:08:46.203Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type in schema"
[2025-06-21T11:08:46.203Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:46.203Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:46.203Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:46.203Z]   [Act] - Step: Executing test logic for "should have Order type in schema"
[2025-06-21T11:08:46.203Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:08:46.203Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:46.203Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:46.203Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:46.203Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T11:08:46.203Z]   [Act Log] - console: 2 outputs
[2025-06-21T11:08:46.203Z]   [Act Log] - process: 2 outputs
[2025-06-21T11:08:46.203Z]   [Act Log] - === Program Output Details ===
[2025-06-21T11:08:46.203Z]   [Act Log] - +24ms [console.log] Console log level: debug
[2025-06-21T11:08:46.203Z]   [Act Log] - +25ms [process.stdout] Console log level: debug

[2025-06-21T11:08:46.203Z]   [Act Log] - +27ms [console.log] File log level: debug
[2025-06-21T11:08:46.203Z]   [Act Log] - +27ms [process.stdout] File log level: debug

[2025-06-21T11:08:46.203Z]   [Act Log] - === End Program Output ===
[2025-06-21T11:08:46.203Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:46.203Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:46.203Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:46.203Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:46.203Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:46.203Z]   [Assert Log] - Expected: Object should have property "Order"
[2025-06-21T11:08:46.203Z]   [Assert Log] - Actual: object.hasOwnProperty("Order") = true
[2025-06-21T11:08:46.203Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Order") - PASSED
[2025-06-21T11:08:46.203Z]   [Assert Log] - Variable: object.Order = [Function] or [Object]
[2025-06-21T11:08:46.203Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:46.203Z]   [CASE END] - Duration: 303ms
[2025-06-21T11:08:46.204Z] 
  [CASE START] - TC-1FF7129A: should have OrderStatus enum in schema
[2025-06-21T11:08:46.204Z]   Module: graphql
[2025-06-21T11:08:46.204Z]   Full Path: Order GraphQL API Integration Tests › Order Schema Types › should have OrderStatus enum in schema
[2025-06-21T11:08:46.235Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderStatus enum in schema"
[2025-06-21T11:08:46.235Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:46.235Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:46.235Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:46.235Z]   [Act] - Step: Executing test logic for "should have OrderStatus enum in schema"
[2025-06-21T11:08:46.235Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:08:46.235Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:46.235Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:46.235Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:46.235Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:46.235Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:46.235Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:46.235Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:46.235Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:46.235Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:46.235Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:46.235Z]   [Assert Log] - Expected: Object should have property "OrderStatus"
[2025-06-21T11:08:46.235Z]   [Assert Log] - Actual: object.hasOwnProperty("OrderStatus") = true
[2025-06-21T11:08:46.235Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("OrderStatus") - PASSED
[2025-06-21T11:08:46.235Z]   [Assert Log] - Variable: object.OrderStatus = [Function] or [Object]
[2025-06-21T11:08:46.235Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:46.235Z]   [CASE END] - Duration: 32ms
[2025-06-21T11:08:46.235Z] 
  [CASE START] - TC-56222F64: should have OrderInput type in schema
[2025-06-21T11:08:46.235Z]   Module: graphql
[2025-06-21T11:08:46.235Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should have OrderInput type in schema
[2025-06-21T11:08:46.264Z]   [Arrange] - Precondition: Test environment prepared for "should have OrderInput type in schema"
[2025-06-21T11:08:46.264Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:46.264Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:46.264Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:46.264Z]   [Act] - Step: Executing test logic for "should have OrderInput type in schema"
[2025-06-21T11:08:46.264Z]   [Act Log] - Loading target module: /test/integration/graphql/order.js
[2025-06-21T11:08:46.265Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:46.265Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:46.265Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:46.265Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:46.265Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:46.265Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:46.265Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:46.265Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:46.265Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:46.265Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:46.265Z]   [Assert Log] - Expected: Object should have property "OrderInput"
[2025-06-21T11:08:46.265Z]   [Assert Log] - Actual: object.hasOwnProperty("OrderInput") = true
[2025-06-21T11:08:46.265Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("OrderInput") - PASSED
[2025-06-21T11:08:46.265Z]   [Assert Log] - Variable: object.OrderInput = [Function] or [Object]
[2025-06-21T11:08:46.265Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:46.265Z]   [CASE END] - Duration: 29ms
[2025-06-21T11:08:46.266Z] 
  [CASE START] - TC-1EE21EDD: should validate GraphQL order operations
[2025-06-21T11:08:46.267Z]   Module: graphql
[2025-06-21T11:08:46.267Z]   Full Path: Order GraphQL API Integration Tests › Order Input Types › should validate GraphQL order operations
[2025-06-21T11:08:46.267Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:46.267Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:46.267Z] 
  [CASE START] - TC-8227B16: should handle malformed order queries
[2025-06-21T11:08:46.267Z]   Module: graphql
[2025-06-21T11:08:46.267Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should handle malformed order queries
[2025-06-21T11:08:46.267Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:46.267Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:46.267Z] 
  [CASE START] - TC-C2E23B4: should validate required arguments
[2025-06-21T11:08:46.267Z]   Module: graphql
[2025-06-21T11:08:46.267Z]   Full Path: Order GraphQL API Integration Tests › GraphQL Error Handling › should validate required arguments
[2025-06-21T11:08:46.267Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:46.267Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:46.267Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/order.test.js
[2025-06-21T11:08:46.267Z] Suite Duration: 1688ms
[2025-06-21T11:08:46.267Z] Suite Results: 3 passed, 0 failed, 3 skipped
[2025-06-21T11:08:46.267Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T11:08:46.267Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:47.561Z] 
  [CASE START] - TC-66DB5068: should respond to GraphQL introspection query
[2025-06-21T11:08:47.561Z]   Module: graphql
[2025-06-21T11:08:47.561Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should respond to GraphQL introspection query
[2025-06-21T11:08:47.624Z]   [Arrange] - Precondition: Test environment prepared for "should respond to GraphQL introspection query"
[2025-06-21T11:08:47.624Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:47.624Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:47.624Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:47.624Z]   [Act] - Step: Executing test logic for "should respond to GraphQL introspection query"
[2025-06-21T11:08:47.624Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T11:08:47.624Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:47.624Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:47.624Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:47.625Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:47.625Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:47.625Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:47.625Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:47.625Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:47.625Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:47.625Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:47.625Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:47.625Z]   [CASE END] - Duration: 63ms
[2025-06-21T11:08:47.625Z] 
  [CASE START] - TC-15E1C257: should have Restaurant type in schema
[2025-06-21T11:08:47.625Z]   Module: graphql
[2025-06-21T11:08:47.625Z]   Full Path: GraphQL Queries Integration Tests › GraphQL Schema Introspection › should have Restaurant type in schema
[2025-06-21T11:08:47.653Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T11:08:47.653Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:47.653Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:47.653Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:47.653Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T11:08:47.653Z]   [Act Log] - Loading target module: /test/integration/graphql/queries.js
[2025-06-21T11:08:47.653Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:47.653Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:47.653Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:47.653Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:47.653Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:47.653Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:47.653Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:47.653Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:47.653Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:47.653Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:47.654Z]   [Assert Log] - Expected: Object should have property "Restaurant"
[2025-06-21T11:08:47.654Z]   [Assert Log] - Actual: object.hasOwnProperty("Restaurant") = true
[2025-06-21T11:08:47.654Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Restaurant") - PASSED
[2025-06-21T11:08:47.654Z]   [Assert Log] - Variable: object.Restaurant = [Function] or [Object]
[2025-06-21T11:08:47.654Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:47.654Z]   [CASE END] - Duration: 28ms
[2025-06-21T11:08:47.655Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/queries.test.js
[2025-06-21T11:08:47.655Z] Suite Duration: 1385ms
[2025-06-21T11:08:47.655Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:08:47.655Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T11:08:47.655Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:48.498Z] 
  [CASE START] - TC-78F89687: should have payment related types in schema
[2025-06-21T11:08:48.498Z]   Module: graphql
[2025-06-21T11:08:48.498Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T11:08:48.554Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T11:08:48.554Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:48.554Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:48.554Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:48.554Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T11:08:48.554Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T11:08:48.554Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:48.554Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:48.554Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:48.554Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:48.554Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:48.554Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:48.554Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:48.554Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:48.554Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:48.554Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:48.554Z]   [Assert Log] - Expected: Object should have property "payment"
[2025-06-21T11:08:48.554Z]   [Assert Log] - Actual: object.hasOwnProperty("payment") = true
[2025-06-21T11:08:48.554Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("payment") - PASSED
[2025-06-21T11:08:48.554Z]   [Assert Log] - Variable: object.payment = [Function] or [Object]
[2025-06-21T11:08:48.554Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:48.554Z]   [CASE END] - Duration: 55ms
[2025-06-21T11:08:48.554Z] 
  [CASE START] - TC-901D3F0: should have query types in schema
[2025-06-21T11:08:48.554Z]   Module: graphql
[2025-06-21T11:08:48.554Z]   Full Path: PayPal Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have query types in schema
[2025-06-21T11:08:48.582Z]   [Arrange] - Precondition: Test environment prepared for "should have query types in schema"
[2025-06-21T11:08:48.582Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:48.582Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:48.582Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:48.582Z]   [Act] - Step: Executing test logic for "should have query types in schema"
[2025-06-21T11:08:48.582Z]   [Act Log] - Loading target module: /test/integration/payment/paypal.js
[2025-06-21T11:08:48.582Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:48.582Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:48.582Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:48.582Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:48.582Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:48.582Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:48.582Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:48.582Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:48.582Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:48.582Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:48.582Z]   [Assert Log] - Expected: Object should have property "query"
[2025-06-21T11:08:48.582Z]   [Assert Log] - Actual: object.hasOwnProperty("query") = true
[2025-06-21T11:08:48.582Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("query") - PASSED
[2025-06-21T11:08:48.582Z]   [Assert Log] - Variable: object.query = [Function] or [Object]
[2025-06-21T11:08:48.582Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:48.582Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:08:48.583Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/paypal.test.js
[2025-06-21T11:08:48.583Z] Suite Duration: 925ms
[2025-06-21T11:08:48.583Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:08:48.583Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T11:08:48.583Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:49.565Z] 
  [CASE START] - TC-43B3D60D: should have payment related types in schema
[2025-06-21T11:08:49.565Z]   Module: graphql
[2025-06-21T11:08:49.565Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have payment related types in schema
[2025-06-21T11:08:49.649Z]   [Arrange] - Precondition: Test environment prepared for "should have payment related types in schema"
[2025-06-21T11:08:49.649Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:49.649Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:49.649Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:49.649Z]   [Act] - Step: Executing test logic for "should have payment related types in schema"
[2025-06-21T11:08:49.649Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T11:08:49.649Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:49.650Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:49.650Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:49.650Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:49.650Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:49.650Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:49.650Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:49.650Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:49.650Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:49.650Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:49.650Z]   [Assert Log] - Expected: Object should have property "payment"
[2025-06-21T11:08:49.650Z]   [Assert Log] - Actual: object.hasOwnProperty("payment") = true
[2025-06-21T11:08:49.650Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("payment") - PASSED
[2025-06-21T11:08:49.650Z]   [Assert Log] - Variable: object.payment = [Function] or [Object]
[2025-06-21T11:08:49.650Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:49.650Z]   [CASE END] - Duration: 84ms
[2025-06-21T11:08:49.650Z] 
  [CASE START] - TC-4C751D51: should have mutation types in schema
[2025-06-21T11:08:49.650Z]   Module: graphql
[2025-06-21T11:08:49.650Z]   Full Path: Stripe Payment Integration Tests (GraphQL Schema) › Payment Related GraphQL Schema › should have mutation types in schema
[2025-06-21T11:08:49.699Z]   [Arrange] - Precondition: Test environment prepared for "should have mutation types in schema"
[2025-06-21T11:08:49.699Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:49.699Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:49.699Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:49.699Z]   [Act] - Step: Executing test logic for "should have mutation types in schema"
[2025-06-21T11:08:49.699Z]   [Act Log] - Loading target module: /test/integration/payment/stripe.js
[2025-06-21T11:08:49.699Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:49.699Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:49.699Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:49.699Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:49.699Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:49.699Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:49.699Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:49.699Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:49.699Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:49.699Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:49.699Z]   [Assert Log] - Expected: Object should have property "mutation"
[2025-06-21T11:08:49.699Z]   [Assert Log] - Actual: object.hasOwnProperty("mutation") = true
[2025-06-21T11:08:49.699Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("mutation") - PASSED
[2025-06-21T11:08:49.699Z]   [Assert Log] - Variable: object.mutation = [Function] or [Object]
[2025-06-21T11:08:49.700Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:49.700Z]   [CASE END] - Duration: 48ms
[2025-06-21T11:08:49.701Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/payment/stripe.test.js
[2025-06-21T11:08:49.701Z] Suite Duration: 1115ms
[2025-06-21T11:08:49.701Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:08:49.702Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T11:08:49.702Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:50.626Z] 
  [CASE START] - TC-6174A2D9: should have Customer type in schema
[2025-06-21T11:08:50.626Z]   Module: graphql
[2025-06-21T11:08:50.627Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Customer type in schema
[2025-06-21T11:08:50.688Z]   [Arrange] - Precondition: Test environment prepared for "should have Customer type in schema"
[2025-06-21T11:08:50.688Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:50.688Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:50.688Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:50.688Z]   [Act] - Step: Executing test logic for "should have Customer type in schema"
[2025-06-21T11:08:50.688Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:08:50.688Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:50.688Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:50.688Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:50.688Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:50.688Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:50.688Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:50.688Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:50.688Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:50.688Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:50.688Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:50.688Z]   [Assert Log] - Expected: Object should have property "Customer"
[2025-06-21T11:08:50.688Z]   [Assert Log] - Actual: object.hasOwnProperty("Customer") = true
[2025-06-21T11:08:50.688Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Customer") - PASSED
[2025-06-21T11:08:50.688Z]   [Assert Log] - Variable: object.Customer = [Function] or [Object]
[2025-06-21T11:08:50.688Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:50.688Z]   [CASE END] - Duration: 61ms
[2025-06-21T11:08:50.689Z] 
  [CASE START] - TC-7138E5AD: should have Address type in schema
[2025-06-21T11:08:50.689Z]   Module: graphql
[2025-06-21T11:08:50.689Z]   Full Path: Customer GraphQL API Integration Tests › Customer Schema Types › should have Address type in schema
[2025-06-21T11:08:50.722Z]   [Arrange] - Precondition: Test environment prepared for "should have Address type in schema"
[2025-06-21T11:08:50.722Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:50.722Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:50.722Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:50.722Z]   [Act] - Step: Executing test logic for "should have Address type in schema"
[2025-06-21T11:08:50.722Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:08:50.722Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:50.722Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:50.722Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:50.722Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:50.722Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:50.723Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:50.723Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:50.723Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:50.723Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:50.723Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:50.723Z]   [Assert Log] - Expected: Object should have property "Address"
[2025-06-21T11:08:50.723Z]   [Assert Log] - Actual: object.hasOwnProperty("Address") = true
[2025-06-21T11:08:50.723Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Address") - PASSED
[2025-06-21T11:08:50.723Z]   [Assert Log] - Variable: object.Address = [Function] or [Object]
[2025-06-21T11:08:50.723Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:50.723Z]   [CASE END] - Duration: 34ms
[2025-06-21T11:08:50.723Z] 
  [CASE START] - TC-6CE70FA2: should have AddressInput type in schema
[2025-06-21T11:08:50.723Z]   Module: graphql
[2025-06-21T11:08:50.723Z]   Full Path: Customer GraphQL API Integration Tests › Customer Input Types › should have AddressInput type in schema
[2025-06-21T11:08:50.758Z]   [Arrange] - Precondition: Test environment prepared for "should have AddressInput type in schema"
[2025-06-21T11:08:50.758Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:50.758Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:50.758Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:50.758Z]   [Act] - Step: Executing test logic for "should have AddressInput type in schema"
[2025-06-21T11:08:50.758Z]   [Act Log] - Loading target module: /test/integration/graphql/customer.js
[2025-06-21T11:08:50.758Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:50.758Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:50.758Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:50.758Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:50.758Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:50.758Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:50.759Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:50.759Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:50.759Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:50.759Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:50.759Z]   [Assert Log] - Expected: Object should have property "AddressInput"
[2025-06-21T11:08:50.759Z]   [Assert Log] - Actual: object.hasOwnProperty("AddressInput") = true
[2025-06-21T11:08:50.759Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("AddressInput") - PASSED
[2025-06-21T11:08:50.759Z]   [Assert Log] - Variable: object.AddressInput = [Function] or [Object]
[2025-06-21T11:08:50.759Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:50.759Z]   [CASE END] - Duration: 35ms
[2025-06-21T11:08:50.761Z] 
  [CASE START] - TC-2C17DA72: should validate customer-related mutations exist
[2025-06-21T11:08:50.761Z]   Module: graphql
[2025-06-21T11:08:50.761Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should validate customer-related mutations exist
[2025-06-21T11:08:50.761Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:50.761Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:50.761Z] 
  [CASE START] - TC-D206B8: should handle GraphQL validation errors
[2025-06-21T11:08:50.761Z]   Module: graphql
[2025-06-21T11:08:50.761Z]   Full Path: Customer GraphQL API Integration Tests › Customer Operations › should handle GraphQL validation errors
[2025-06-21T11:08:50.761Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:50.761Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:50.761Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/customer.test.js
[2025-06-21T11:08:50.761Z] Suite Duration: 1053ms
[2025-06-21T11:08:50.761Z] Suite Results: 3 passed, 0 failed, 2 skipped
[2025-06-21T11:08:50.762Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T11:08:50.762Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:51.569Z] 
  [CASE START] - TC-41DA358E: should have order query in schema
[2025-06-21T11:08:51.570Z]   Module: order
[2025-06-21T11:08:51.570Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have order query in schema
[2025-06-21T11:08:51.627Z]   [Arrange] - Precondition: Test environment prepared for "should have order query in schema"
[2025-06-21T11:08:51.627Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:51.627Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:51.627Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:51.627Z]   [Act] - Step: Executing test logic for "should have order query in schema"
[2025-06-21T11:08:51.627Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T11:08:51.627Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:51.627Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:51.627Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:51.627Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:51.627Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:51.627Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:51.627Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:51.627Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:51.627Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:51.627Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:51.627Z]   [Assert Log] - Expected: Object should have property "order"
[2025-06-21T11:08:51.627Z]   [Assert Log] - Actual: object.hasOwnProperty("order") = true
[2025-06-21T11:08:51.627Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("order") - PASSED
[2025-06-21T11:08:51.627Z]   [Assert Log] - Variable: object.order = [Function] or [Object]
[2025-06-21T11:08:51.627Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:51.627Z]   [CASE END] - Duration: 57ms
[2025-06-21T11:08:51.627Z] 
  [CASE START] - TC-2A4E4C9: should have orders query in schema
[2025-06-21T11:08:51.627Z]   Module: order
[2025-06-21T11:08:51.628Z]   Full Path: Order Management Integration Tests › Order GraphQL Schema Tests › should have orders query in schema
[2025-06-21T11:08:51.654Z]   [Arrange] - Precondition: Test environment prepared for "should have orders query in schema"
[2025-06-21T11:08:51.654Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:51.654Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:51.654Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:51.654Z]   [Act] - Step: Executing test logic for "should have orders query in schema"
[2025-06-21T11:08:51.654Z]   [Act Log] - Loading target module: /test/integration/order/orderManagement.js
[2025-06-21T11:08:51.654Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:51.654Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:51.654Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:51.654Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:51.655Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:51.655Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:51.655Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:51.655Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:51.655Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:51.655Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:51.655Z]   [Assert Log] - Expected: Object should have property "orders"
[2025-06-21T11:08:51.655Z]   [Assert Log] - Actual: object.hasOwnProperty("orders") = true
[2025-06-21T11:08:51.655Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("orders") - PASSED
[2025-06-21T11:08:51.655Z]   [Assert Log] - Variable: object.orders = [Function] or [Object]
[2025-06-21T11:08:51.655Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:51.655Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:08:51.656Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderManagement.test.js
[2025-06-21T11:08:51.656Z] Suite Duration: 890ms
[2025-06-21T11:08:51.656Z] Suite Results: 2 passed, 0 failed, 0 skipped
[2025-06-21T11:08:51.656Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T11:08:51.656Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:52.367Z] 
  [CASE START] - TC-50629841: should have updateOrderStatus mutation in schema
[2025-06-21T11:08:52.367Z]   Module: graphql
[2025-06-21T11:08:52.367Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have updateOrderStatus mutation in schema
[2025-06-21T11:08:52.426Z]   [Arrange] - Precondition: Test environment prepared for "should have updateOrderStatus mutation in schema"
[2025-06-21T11:08:52.426Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:52.426Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:52.426Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:52.426Z]   [Act] - Step: Executing test logic for "should have updateOrderStatus mutation in schema"
[2025-06-21T11:08:52.426Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:08:52.426Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:52.426Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:52.426Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:52.426Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:52.426Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:52.426Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:52.426Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:52.426Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:52.426Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:52.426Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:52.426Z]   [Assert Log] - Expected: Object should have property "updateOrderStatus"
[2025-06-21T11:08:52.426Z]   [Assert Log] - Actual: object.hasOwnProperty("updateOrderStatus") = true
[2025-06-21T11:08:52.426Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("updateOrderStatus") - PASSED
[2025-06-21T11:08:52.426Z]   [Assert Log] - Variable: object.updateOrderStatus = [Function] or [Object]
[2025-06-21T11:08:52.426Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:52.427Z]   [CASE END] - Duration: 59ms
[2025-06-21T11:08:52.427Z] 
  [CASE START] - TC-2C57D683: should have Order type with orderStatus field in schema
[2025-06-21T11:08:52.427Z]   Module: graphql
[2025-06-21T11:08:52.427Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have Order type with orderStatus field in schema
[2025-06-21T11:08:52.451Z]   [Arrange] - Precondition: Test environment prepared for "should have Order type with orderStatus field in schema"
[2025-06-21T11:08:52.451Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:52.451Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:52.451Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:52.451Z]   [Act] - Step: Executing test logic for "should have Order type with orderStatus field in schema"
[2025-06-21T11:08:52.451Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:08:52.451Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:52.451Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:52.451Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:52.451Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:52.451Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:52.451Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:52.451Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:52.451Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:52.451Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:52.451Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:52.451Z]   [Assert Log] - Expected: Object should have property "Order"
[2025-06-21T11:08:52.451Z]   [Assert Log] - Actual: object.hasOwnProperty("Order") = true
[2025-06-21T11:08:52.451Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Order") - PASSED
[2025-06-21T11:08:52.451Z]   [Assert Log] - Variable: object.Order = [Function] or [Object]
[2025-06-21T11:08:52.451Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:52.451Z]   [CASE END] - Duration: 24ms
[2025-06-21T11:08:52.451Z] 
  [CASE START] - TC-7C91AF0C: should have order status enum values in schema
[2025-06-21T11:08:52.451Z]   Module: graphql
[2025-06-21T11:08:52.451Z]   Full Path: Order State Machine Integration Tests (GraphQL) › GraphQL Order Status Updates › should have order status enum values in schema
[2025-06-21T11:08:52.477Z]   [Arrange] - Precondition: Test environment prepared for "should have order status enum values in schema"
[2025-06-21T11:08:52.477Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:52.477Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:52.477Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:52.477Z]   [Act] - Step: Executing test logic for "should have order status enum values in schema"
[2025-06-21T11:08:52.477Z]   [Act Log] - Loading target module: /test/integration/order/orderStateMachine.js
[2025-06-21T11:08:52.477Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:52.477Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:52.477Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:52.477Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:52.477Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:52.477Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:52.477Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:52.477Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:52.477Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:52.477Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:52.477Z]   [Assert Log] - Expected: Object should have property "order"
[2025-06-21T11:08:52.477Z]   [Assert Log] - Actual: object.hasOwnProperty("order") = true
[2025-06-21T11:08:52.477Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("order") - PASSED
[2025-06-21T11:08:52.477Z]   [Assert Log] - Variable: object.order = [Function] or [Object]
[2025-06-21T11:08:52.477Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:52.477Z]   [CASE END] - Duration: 25ms
[2025-06-21T11:08:52.478Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderStateMachine.test.js
[2025-06-21T11:08:52.478Z] Suite Duration: 818ms
[2025-06-21T11:08:52.478Z] Suite Results: 3 passed, 0 failed, 0 skipped
[2025-06-21T11:08:52.478Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T11:08:52.478Z] Suite Display Name: INTEGRATION
[2025-06-21T11:08:53.160Z] 
  [CASE START] - TC-B97C03F: should have Restaurant type in schema
[2025-06-21T11:08:53.160Z]   Module: graphql
[2025-06-21T11:08:53.160Z]   Full Path: Restaurant GraphQL API Integration Tests › Restaurant Schema › should have Restaurant type in schema
[2025-06-21T11:08:53.216Z]   [Arrange] - Precondition: Test environment prepared for "should have Restaurant type in schema"
[2025-06-21T11:08:53.216Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:53.216Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:53.216Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:53.216Z]   [Act] - Step: Executing test logic for "should have Restaurant type in schema"
[2025-06-21T11:08:53.216Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:08:53.216Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:53.216Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:53.216Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:53.216Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:53.216Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:53.216Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:53.216Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:53.216Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:53.216Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:53.216Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:53.216Z]   [Assert Log] - Expected: Object should have property "Restaurant"
[2025-06-21T11:08:53.216Z]   [Assert Log] - Actual: object.hasOwnProperty("Restaurant") = true
[2025-06-21T11:08:53.216Z]   [Assert Log] - Comparison: expect(object).toHaveProperty("Restaurant") - PASSED
[2025-06-21T11:08:53.216Z]   [Assert Log] - Variable: object.Restaurant = [Function] or [Object]
[2025-06-21T11:08:53.216Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:53.216Z]   [CASE END] - Duration: 56ms
[2025-06-21T11:08:53.216Z] 
  [CASE START] - TC-35BB4889: should support schema introspection
[2025-06-21T11:08:53.216Z]   Module: graphql
[2025-06-21T11:08:53.216Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should support schema introspection
[2025-06-21T11:08:53.241Z]   [Arrange] - Precondition: Test environment prepared for "should support schema introspection"
[2025-06-21T11:08:53.242Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:53.242Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:53.242Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:53.242Z]   [Act] - Step: Executing test logic for "should support schema introspection"
[2025-06-21T11:08:53.242Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:08:53.242Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:53.242Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:53.242Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:53.242Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:53.242Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:53.242Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:53.242Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:53.242Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:53.242Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:53.242Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:53.242Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:53.242Z]   [CASE END] - Duration: 25ms
[2025-06-21T11:08:53.242Z] 
  [CASE START] - TC-6C7A3797: should list available queries
[2025-06-21T11:08:53.242Z]   Module: graphql
[2025-06-21T11:08:53.242Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available queries
[2025-06-21T11:08:53.266Z]   [Arrange] - Precondition: Test environment prepared for "should list available queries"
[2025-06-21T11:08:53.266Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:53.266Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:53.266Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:53.266Z]   [Act] - Step: Executing test logic for "should list available queries"
[2025-06-21T11:08:53.266Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:08:53.266Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:53.266Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:53.266Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:53.266Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:53.266Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:53.266Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:53.266Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:53.266Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:53.266Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:53.266Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:53.266Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:53.266Z]   [CASE END] - Duration: 24ms
[2025-06-21T11:08:53.267Z] 
  [CASE START] - TC-753B7F2D: should list available mutations
[2025-06-21T11:08:53.267Z]   Module: graphql
[2025-06-21T11:08:53.267Z]   Full Path: Restaurant GraphQL API Integration Tests › Schema Introspection › should list available mutations
[2025-06-21T11:08:53.294Z]   [Arrange] - Precondition: Test environment prepared for "should list available mutations"
[2025-06-21T11:08:53.294Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T11:08:53.294Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T11:08:53.294Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T11:08:53.294Z]   [Act] - Step: Executing test logic for "should list available mutations"
[2025-06-21T11:08:53.294Z]   [Act Log] - Loading target module: /test/integration/graphql/restaurant.js
[2025-06-21T11:08:53.294Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T11:08:53.294Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T11:08:53.294Z]   [Act Log] - Invoking target method or function
[2025-06-21T11:08:53.294Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T11:08:53.294Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T11:08:53.294Z]   [Act Log] - Method execution completed successfully
[2025-06-21T11:08:53.294Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T11:08:53.294Z]   [Act Log] - All function calls returned expected types
[2025-06-21T11:08:53.294Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T11:08:53.294Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T11:08:53.294Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T11:08:53.294Z]   [CASE END] - Duration: 27ms
[2025-06-21T11:08:53.295Z] 
  [CASE START] - TC-387C915A: should respond to GraphQL endpoint
[2025-06-21T11:08:53.295Z]   Module: graphql
[2025-06-21T11:08:53.295Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should respond to GraphQL endpoint
[2025-06-21T11:08:53.295Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:53.295Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:53.295Z] 
  [CASE START] - TC-21545EE6: should handle invalid GraphQL queries
[2025-06-21T11:08:53.295Z]   Module: graphql
[2025-06-21T11:08:53.295Z]   Full Path: Restaurant GraphQL API Integration Tests › GraphQL Endpoint › should handle invalid GraphQL queries
[2025-06-21T11:08:53.295Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:53.295Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:53.295Z] 
  [CASE START] - TC-18BF1AAD: should handle simple queries
[2025-06-21T11:08:53.295Z]   Module: graphql
[2025-06-21T11:08:53.295Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle simple queries
[2025-06-21T11:08:53.295Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:53.295Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:53.296Z] 
  [CASE START] - TC-14898FE1: should validate GraphQL syntax
[2025-06-21T11:08:53.296Z]   Module: graphql
[2025-06-21T11:08:53.296Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should validate GraphQL syntax
[2025-06-21T11:08:53.296Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:53.296Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:53.296Z] 
  [CASE START] - TC-63BF36C2: should handle empty queries
[2025-06-21T11:08:53.296Z]   Module: graphql
[2025-06-21T11:08:53.296Z]   Full Path: Restaurant GraphQL API Integration Tests › Basic GraphQL Operations › should handle empty queries
[2025-06-21T11:08:53.296Z]   [RESULT] - SKIP: Test marked as pending/skipped
[2025-06-21T11:08:53.296Z]   [CASE END] - Duration: 0ms
[2025-06-21T11:08:53.296Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/graphql/restaurant.test.js
[2025-06-21T11:08:53.296Z] Suite Duration: 815ms
[2025-06-21T11:08:53.296Z] Suite Results: 4 passed, 0 failed, 5 skipped
[2025-06-21T11:08:53.296Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/integration/order/orderNotifications.test.js
[2025-06-21T11:08:53.296Z] Suite Display Name: INTEGRATION
