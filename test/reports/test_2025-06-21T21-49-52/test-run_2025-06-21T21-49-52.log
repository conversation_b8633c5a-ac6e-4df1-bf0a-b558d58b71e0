[2025-06-21T21:49:52.514Z] ================================================================================
[2025-06-21T21:49:52.514Z] Test Run Started
[2025-06-21T21:49:52.514Z] Timestamp: 2025-06-21T21:49:52.514Z
[2025-06-21T21:49:52.514Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T21:49:52.514Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:49:52.514Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:49:52.515Z] ================================================================================
[2025-06-21T21:49:52.518Z] 
[RUN START] - Test execution beginning
[2025-06-21T21:49:52.518Z] Total Test Suites: 1
[2025-06-21T21:49:52.518Z] Test Environment: test
[2025-06-21T21:49:52.527Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/simple-log-test.test.js
[2025-06-21T21:49:52.527Z] Suite Display Name: UNIT
[2025-06-21T21:49:53.022Z] 
  [CASE START] - TC-261F646D: should show console and winston output in logs
[2025-06-21T21:49:53.022Z]   Module: simple
[2025-06-21T21:49:53.022Z]   Full Path: Simple Log Test › should show console and winston output in logs
[2025-06-21T21:49:53.080Z]   [Arrange] - Precondition: Test environment prepared for "should show console and winston output in logs"
[2025-06-21T21:49:53.080Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:49:53.080Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:49:53.080Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:49:53.080Z]   [Act] - Step: Executing test logic for "should show console and winston output in logs"
[2025-06-21T21:49:53.080Z]   [Act Log] - Loading target module: simple-log-test.js
[2025-06-21T21:49:53.080Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:49:53.080Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:49:53.080Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:49:53.080Z]   [Act Log] - Captured 9 program outputs during execution
[2025-06-21T21:49:53.080Z]   [Act Log] - console: 2 outputs
[2025-06-21T21:49:53.080Z]   [Act Log] - process: 7 outputs
[2025-06-21T21:49:53.080Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:49:53.080Z]   [Act Log] - +28ms [console.log] Console log level: info
[2025-06-21T21:49:53.080Z]   [Act Log] - +29ms [process.stdout] Console log level: info

[2025-06-21T21:49:53.080Z]   [Act Log] - +32ms [console.log] File log level: warn
[2025-06-21T21:49:53.080Z]   [Act Log] - +32ms [process.stdout] File log level: warn

[2025-06-21T21:49:53.080Z]   [Act Log] - +86ms [process.stdout] 2025-06-21 22:49:53:4953 [32minfo[39m: [32m=== WINSTON TEST START ===[39m

[2025-06-21T21:49:53.080Z]   [Act Log] - +87ms [process.stdout] 2025-06-21 22:49:53:4953 [32minfo[39m: [32mThis is a winston info message[39m

[2025-06-21T21:49:53.080Z]   [Act Log] - +87ms [process.stdout] 2025-06-21 22:49:53:4953 [33mwarn[39m: [33mThis is a winston warn message[39m

[2025-06-21T21:49:53.080Z]   [Act Log] - +87ms [process.stdout] 2025-06-21 22:49:53:4953 [31merror[39m: [31mThis is a winston error message[39m

[2025-06-21T21:49:53.080Z]   [Act Log] - +88ms [process.stdout] 2025-06-21 22:49:53:4953 [32minfo[39m: [32m=== WINSTON TEST END ===[39m

[2025-06-21T21:49:53.080Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:49:53.080Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:49:53.080Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:49:53.080Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:49:53.080Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:49:53.080Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:49:53.080Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:53.080Z]   [CASE END] - Duration: 91ms
[2025-06-21T21:49:53.090Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/simple-log-test.test.js
[2025-06-21T21:49:53.090Z] Suite Duration: 537ms
[2025-06-21T21:49:53.090Z] Suite Results: 1 passed, 0 failed, 0 skipped
[2025-06-21T21:49:53.096Z] 
================================================================================
[2025-06-21T21:49:53.096Z] Test Run Finished
[2025-06-21T21:49:53.096Z] End Timestamp: 2025-06-21T21:49:53.096Z
[2025-06-21T21:49:53.096Z] Total Duration: 582ms (0.58s)
[2025-06-21T21:49:53.096Z] 
[STATISTICS]
[2025-06-21T21:49:53.096Z] Total Test Suites: 1
[2025-06-21T21:49:53.096Z] Passed Test Suites: 1
[2025-06-21T21:49:53.096Z] Failed Test Suites: 0
[2025-06-21T21:49:53.096Z] Total Tests: 1
[2025-06-21T21:49:53.096Z] Passed Tests: 1
[2025-06-21T21:49:53.096Z] Failed Tests: 0
[2025-06-21T21:49:53.096Z] Skipped Tests: 0
[2025-06-21T21:49:53.096Z] Success Rate: 100.00%
[2025-06-21T21:49:53.096Z] Overall Result: FAILURE
[2025-06-21T21:49:53.096Z] ================================================================================
