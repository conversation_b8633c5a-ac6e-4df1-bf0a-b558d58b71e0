[2025-06-21T21:49:21.271Z] ================================================================================
[2025-06-21T21:49:21.272Z] Test Run Started
[2025-06-21T21:49:21.272Z] Timestamp: 2025-06-21T21:49:21.272Z
[2025-06-21T21:49:21.272Z] Operating System: linux x64
[2025-06-21T21:49:21.272Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:49:21.272Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:49:21.272Z] ================================================================================
[2025-06-21T21:49:22.060Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:49:22.061Z] 
[CASE START] - U-AUTO-001: should capture console.log output
[2025-06-21T21:49:22.061Z] Module: log
[2025-06-21T21:49:22.061Z] Full Path: Log Capture Test › should capture console.log output
[2025-06-21T21:49:22.061Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:49:22.061Z] [Act] - Step: Executing test logic
[2025-06-21T21:49:22.061Z] [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:49:22.061Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:49:22.061Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:49:22.061Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:49:22.061Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.061Z] [CASE END] - Duration: 48ms
[2025-06-21T21:49:22.061Z] 
[CASE START] - U-AUTO-002: should capture winston logger output
[2025-06-21T21:49:22.061Z] Module: log
[2025-06-21T21:49:22.061Z] Full Path: Log Capture Test › should capture winston logger output
[2025-06-21T21:49:22.061Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:49:22.061Z] [Act] - Step: Executing test logic
[2025-06-21T21:49:22.061Z] [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:49:22.061Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:49:22.061Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:49:22.061Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:49:22.061Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.061Z] [CASE END] - Duration: 20ms
[2025-06-21T21:49:22.062Z] 
[CASE START] - U-AUTO-003: should capture mixed console and winston output
[2025-06-21T21:49:22.062Z] Module: log
[2025-06-21T21:49:22.062Z] Full Path: Log Capture Test › should capture mixed console and winston output
[2025-06-21T21:49:22.062Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:49:22.062Z] [Act] - Step: Executing test logic
[2025-06-21T21:49:22.062Z] [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:49:22.062Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:49:22.062Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:49:22.062Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:49:22.062Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.062Z] [CASE END] - Duration: 18ms
[2025-06-21T21:49:22.062Z] 
[CASE START] - U-AUTO-004: should capture error scenarios
[2025-06-21T21:49:22.062Z] Module: log
[2025-06-21T21:49:22.062Z] Full Path: Log Capture Test › should capture error scenarios
[2025-06-21T21:49:22.062Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:49:22.062Z] [Act] - Step: Executing test logic
[2025-06-21T21:49:22.062Z] [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:49:22.062Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:49:22.062Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:49:22.062Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:49:22.062Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.062Z] [CASE END] - Duration: 16ms
[2025-06-21T21:49:22.062Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:49:22.067Z] 
================================================================================
[2025-06-21T21:49:22.067Z] Test Run Finished
[2025-06-21T21:49:22.067Z] End Timestamp: 2025-06-21T21:49:22.067Z
[2025-06-21T21:49:22.067Z] Total Duration: 796ms (0.80s)
[2025-06-21T21:49:22.067Z] Total Tests: 4
[2025-06-21T21:49:22.067Z] Passed Tests: 4
[2025-06-21T21:49:22.067Z] Failed Tests: 0
[2025-06-21T21:49:22.067Z] Skipped Tests: 0
[2025-06-21T21:49:22.067Z] Success Rate: 100.00%
[2025-06-21T21:49:22.067Z] Overall Result: FAILURE
[2025-06-21T21:49:22.067Z] ================================================================================
