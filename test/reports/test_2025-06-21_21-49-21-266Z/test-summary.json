{"summary": {"totalTests": 4, "passedTests": 4, "failedTests": 0, "skippedTests": 0, "totalDuration": 796, "success": false, "timestamp": "2025-06-21T21:49:22.067Z", "environment": "test"}, "projects": {"UNIT": {"tests": [{"caseId": "U-AUTO-001", "title": "should capture console.log output", "status": "passed", "duration": 48, "failureMessages": [], "ancestorTitles": ["Log Capture Test"], "capturedLogs": 0}, {"caseId": "U-AUTO-002", "title": "should capture winston logger output", "status": "passed", "duration": 20, "failureMessages": [], "ancestorTitles": ["Log Capture Test"], "capturedLogs": 0}, {"caseId": "U-AUTO-003", "title": "should capture mixed console and winston output", "status": "passed", "duration": 18, "failureMessages": [], "ancestorTitles": ["Log Capture Test"], "capturedLogs": 0}, {"caseId": "U-AUTO-004", "title": "should capture error scenarios", "status": "passed", "duration": 16, "failureMessages": [], "ancestorTitles": ["Log Capture Test"], "capturedLogs": 0}], "summary": {"total": 4, "passed": 4, "failed": 0, "skipped": 0, "duration": 102}}}, "coverage": {}, "performance": {}}