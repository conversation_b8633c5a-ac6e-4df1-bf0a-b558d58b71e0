[2025-06-21T21:42:43.966Z] ================================================================================
[2025-06-21T21:42:43.966Z] Test Run Started
[2025-06-21T21:42:43.966Z] Timestamp: 2025-06-21T21:42:43.966Z
[2025-06-21T21:42:43.966Z] Operating System: linux x64
[2025-06-21T21:42:43.966Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:42:43.966Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:42:43.966Z] ================================================================================
[2025-06-21T21:42:56.887Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T21:42:56.888Z] 
[CASE START] - U-AUTO-001: should transition from initial to restaurant selection on message received
[2025-06-21T21:42:56.888Z] Module: dialog
[2025-06-21T21:42:56.888Z] Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T21:42:56.888Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.888Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.888Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:42:56.888Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:42:56.888Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:42:56.888Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.888Z] [CASE END] - Duration: 63ms
[2025-06-21T21:42:56.888Z] 
[CASE START] - U-AUTO-002: should transition through the order flow
[2025-06-21T21:42:56.889Z] Module: dialog
[2025-06-21T21:42:56.889Z] Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T21:42:56.889Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.889Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.889Z] [Assert] - Verifying: Test assertions failed
[2025-06-21T21:42:56.889Z] [Assert Log] - Expected: Test should pass
[2025-06-21T21:42:56.889Z] [Assert Log] - Actual: Error: thrown: "Exceeded timeout of 10000 ms for a test.
[2025-06-21T21:42:56.889Z] [RESULT] - FAIL: Error: thrown: "Exceeded timeout of 10000 ms for a test.
[2025-06-21T21:42:56.889Z] [CASE END] - Duration: 10001ms
[2025-06-21T21:42:56.889Z] 
[CASE START] - U-AUTO-003: should handle payment failure
[2025-06-21T21:42:56.889Z] Module: dialog
[2025-06-21T21:42:56.889Z] Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T21:42:56.889Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.889Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.889Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:42:56.889Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:42:56.889Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:42:56.889Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.889Z] [CASE END] - Duration: 2ms
[2025-06-21T21:42:56.889Z] 
[CASE START] - U-AUTO-004: should start and stop dialog manager service
[2025-06-21T21:42:56.889Z] Module: dialog
[2025-06-21T21:42:56.889Z] Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T21:42:56.889Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.889Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.889Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:42:56.889Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:42:56.889Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:42:56.889Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.889Z] [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.890Z] 
[CASE START] - U-AUTO-005: should properly clean up mocks and reset state
[2025-06-21T21:42:56.890Z] Module: dialog
[2025-06-21T21:42:56.890Z] Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T21:42:56.890Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.890Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.890Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:42:56.890Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:42:56.890Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:42:56.890Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.890Z] [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.890Z] 
[CASE START] - U-AUTO-006: should handle unknown event types gracefully
[2025-06-21T21:42:56.890Z] Module: dialog
[2025-06-21T21:42:56.890Z] Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T21:42:56.890Z] [Arrange] - Precondition: Setting up test environment
[2025-06-21T21:42:56.890Z] [Act] - Step: Executing test logic
[2025-06-21T21:42:56.890Z] [Assert] - Verifying: All assertions passed
[2025-06-21T21:42:56.890Z] [Assert Log] - Expected: Test completion without errors
[2025-06-21T21:42:56.890Z] [Assert Log] - Actual: Test completed successfully
[2025-06-21T21:42:56.890Z] [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.890Z] [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.890Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T21:42:56.895Z] 
================================================================================
[2025-06-21T21:42:56.895Z] Test Run Finished
[2025-06-21T21:42:56.895Z] End Timestamp: 2025-06-21T21:42:56.895Z
[2025-06-21T21:42:56.895Z] Total Duration: 12929ms (12.93s)
[2025-06-21T21:42:56.895Z] Total Tests: 6
[2025-06-21T21:42:56.895Z] Passed Tests: 5
[2025-06-21T21:42:56.895Z] Failed Tests: 1
[2025-06-21T21:42:56.895Z] Skipped Tests: 0
[2025-06-21T21:42:56.895Z] Success Rate: 83.33%
[2025-06-21T21:42:56.895Z] Overall Result: FAILURE
[2025-06-21T21:42:56.895Z] ================================================================================
