{"summary": {"totalTests": 6, "passedTests": 5, "failedTests": 1, "skippedTests": 0, "totalDuration": 12929, "success": false, "timestamp": "2025-06-21T21:42:56.895Z", "environment": "test"}, "projects": {"UNIT": {"tests": [{"caseId": "U-AUTO-001", "title": "should transition from initial to restaurant selection on message received", "status": "passed", "duration": 63, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"caseId": "U-AUTO-002", "title": "should transition through the order flow", "status": "failed", "duration": 10001, "failureMessages": ["Error: thrown: \"Exceeded timeout of 10000 ms for a test.\nAdd a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout.\"\n    at test (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js:121:5)\n    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)\n    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)\n    at describe (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js:81:3)\n    at _dispatchDescribe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:91:26)\n    at describe (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/index.js:55:5)\n    at Object.describe (/home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js:47:1)\n    at Runtime._execModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1439:24)\n    at Runtime._loadModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:1022:12)\n    at Runtime.requireModule (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runtime/build/index.js:882:12)\n    at jestAdapter (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:77:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at runTestInternal (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/home/<USER>/firespoon/Firespoon_API_TF/node_modules/jest-runner/build/runTest.js:444:34)"], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"caseId": "U-AUTO-003", "title": "should handle payment failure", "status": "passed", "duration": 2, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "基础状态转换测试"]}, {"caseId": "U-AUTO-004", "title": "should start and stop dialog manager service", "status": "passed", "duration": 1, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}, {"caseId": "U-AUTO-005", "title": "should properly clean up mocks and reset state", "status": "passed", "duration": 1, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}, {"caseId": "U-AUTO-006", "title": "should handle unknown event types gracefully", "status": "passed", "duration": 1, "failureMessages": [], "ancestorTitles": ["Dialog Manager", "状态机服务生命周期管理"]}], "summary": {"total": 6, "passed": 5, "failed": 1, "skipped": 0, "duration": 10069}}}, "coverage": {}, "performance": {}}