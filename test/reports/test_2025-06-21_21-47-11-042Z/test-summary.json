{"summary": {"totalTests": 4, "passedTests": 4, "failedTests": 0, "skippedTests": 0, "totalDuration": 714, "success": false, "timestamp": "2025-06-21T21:47:11.760Z", "environment": "test"}, "projects": {"UNIT": {"tests": [{"caseId": "U-AUTO-001", "title": "should capture console.log output", "status": "passed", "duration": 34, "failureMessages": [], "ancestorTitles": ["Log Capture Test"]}, {"caseId": "U-AUTO-002", "title": "should capture winston logger output", "status": "passed", "duration": 5, "failureMessages": [], "ancestorTitles": ["Log Capture Test"]}, {"caseId": "U-AUTO-003", "title": "should capture mixed console and winston output", "status": "passed", "duration": 1, "failureMessages": [], "ancestorTitles": ["Log Capture Test"]}, {"caseId": "U-AUTO-004", "title": "should capture error scenarios", "status": "passed", "duration": 1, "failureMessages": [], "ancestorTitles": ["Log Capture Test"]}], "summary": {"total": 4, "passed": 4, "failed": 0, "skipped": 0, "duration": 41}}}, "coverage": {}, "performance": {}}