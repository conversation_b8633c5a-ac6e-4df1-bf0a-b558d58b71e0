[2025-06-21T21:42:43.961Z] ================================================================================
[2025-06-21T21:42:43.962Z] Test Run Started
[2025-06-21T21:42:43.962Z] Timestamp: 2025-06-21T21:42:43.961Z
[2025-06-21T21:42:43.962Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T21:42:43.962Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:42:43.962Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:42:43.962Z] ================================================================================
[2025-06-21T21:42:43.966Z] 
[RUN START] - Test execution beginning
[2025-06-21T21:42:43.967Z] Total Test Suites: 1
[2025-06-21T21:42:43.967Z] Test Environment: test
[2025-06-21T21:42:43.981Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T21:42:43.981Z] Suite Display Name: UNIT
[2025-06-21T21:42:46.842Z] 
  [CASE START] - TC-7299DE45: should transition from initial to restaurant selection on message received
[2025-06-21T21:42:46.842Z]   Module: dialog
[2025-06-21T21:42:46.842Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition from initial to restaurant selection on message received
[2025-06-21T21:42:46.849Z]   [Arrange] - Precondition: Test environment prepared for "should transition from initial to restaurant selection on message received"
[2025-06-21T21:42:46.849Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:46.849Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:46.849Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:46.849Z]   [Act] - Step: Executing test logic for "should transition from initial to restaurant selection on message received"
[2025-06-21T21:42:46.849Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:46.849Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:46.849Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:46.849Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:46.849Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T21:42:46.849Z]   [Act Log] - console: 2 outputs
[2025-06-21T21:42:46.849Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:42:46.849Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:42:46.849Z]   [Act Log] - +53ms [console.log] Console log level: info
[2025-06-21T21:42:46.849Z]   [Act Log] - +53ms [process.stdout] Console log level: info

[2025-06-21T21:42:46.849Z]   [Act Log] - +56ms [console.log] File log level: warn
[2025-06-21T21:42:46.849Z]   [Act Log] - +56ms [process.stdout] File log level: warn

[2025-06-21T21:42:46.849Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:42:46.849Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:42:46.849Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:42:46.849Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:42:46.849Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:42:46.849Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:42:46.849Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:46.850Z]   [CASE END] - Duration: 63ms
[2025-06-21T21:42:46.850Z] 
  [CASE START] - TC-1B723CE: should transition through the order flow
[2025-06-21T21:42:46.850Z]   Module: dialog
[2025-06-21T21:42:46.850Z]   Full Path: Dialog Manager › 基础状态转换测试 › should transition through the order flow
[2025-06-21T21:42:56.861Z]   [Arrange] - Precondition: Test environment prepared for "should transition through the order flow"
[2025-06-21T21:42:56.861Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:56.861Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:56.861Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:56.862Z]   [Act] - Step: Executing test logic for "should transition through the order flow"
[2025-06-21T21:42:56.862Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:56.862Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:56.862Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:56.862Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:56.862Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T21:42:56.862Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:42:56.862Z]   [Act Log] - Exception caught during execution
[2025-06-21T21:42:56.862Z]   [Act Log] - Error type: Error
[2025-06-21T21:42:56.862Z]   [Act Log] - Error message: Error: thrown: "Exceeded timeout of 10000 ms for a test.
[2025-06-21T21:42:56.862Z]   [Act Log] - Stack trace available: Yes
[2025-06-21T21:42:56.862Z]   [Assert] - Verifying: Expected success but encountered failure
[2025-06-21T21:42:56.862Z]   [Assert Log] - Expected result: Test should pass without errors
[2025-06-21T21:42:56.862Z]   [Assert Log] - Actual result: Error: thrown: "Exceeded timeout of 10000 ms for a test.
[2025-06-21T21:42:56.862Z]   [Assert Log] - Comparison status: FAILED - Expected !== Actual
[2025-06-21T21:42:56.862Z]   [Assert Log] - Error details: Stack trace: Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."
[2025-06-21T21:42:56.862Z]   [RESULT] - FAIL: Error: thrown: "Exceeded timeout of 10000 ms for a test.
[2025-06-21T21:42:56.862Z]   [CASE END] - Duration: 10001ms
[2025-06-21T21:42:56.862Z] 
  [CASE START] - TC-324CF301: should handle payment failure
[2025-06-21T21:42:56.862Z]   Module: dialog
[2025-06-21T21:42:56.862Z]   Full Path: Dialog Manager › 基础状态转换测试 › should handle payment failure
[2025-06-21T21:42:56.864Z]   [Arrange] - Precondition: Test environment prepared for "should handle payment failure"
[2025-06-21T21:42:56.864Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:56.864Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:56.864Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:56.864Z]   [Act] - Step: Executing test logic for "should handle payment failure"
[2025-06-21T21:42:56.864Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:56.864Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:56.864Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:56.864Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:56.864Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T21:42:56.864Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:42:56.864Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:42:56.864Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:42:56.864Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:42:56.864Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:42:56.864Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:42:56.864Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.864Z]   [CASE END] - Duration: 2ms
[2025-06-21T21:42:56.865Z] 
  [CASE START] - TC-44064: should start and stop dialog manager service
[2025-06-21T21:42:56.865Z]   Module: dialog
[2025-06-21T21:42:56.865Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should start and stop dialog manager service
[2025-06-21T21:42:56.866Z]   [Arrange] - Precondition: Test environment prepared for "should start and stop dialog manager service"
[2025-06-21T21:42:56.866Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:56.866Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:56.866Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:56.866Z]   [Act] - Step: Executing test logic for "should start and stop dialog manager service"
[2025-06-21T21:42:56.866Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:56.866Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:56.866Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:56.866Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:56.866Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T21:42:56.866Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:42:56.866Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:42:56.866Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:42:56.866Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:42:56.866Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:42:56.866Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:42:56.866Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.866Z]   [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.866Z] 
  [CASE START] - TC-67EE08B0: should properly clean up mocks and reset state
[2025-06-21T21:42:56.866Z]   Module: dialog
[2025-06-21T21:42:56.866Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should properly clean up mocks and reset state
[2025-06-21T21:42:56.867Z]   [Arrange] - Precondition: Test environment prepared for "should properly clean up mocks and reset state"
[2025-06-21T21:42:56.867Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:56.867Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:56.867Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:56.867Z]   [Act] - Step: Executing test logic for "should properly clean up mocks and reset state"
[2025-06-21T21:42:56.867Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:56.867Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:56.867Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:56.867Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:56.867Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T21:42:56.867Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:42:56.867Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:42:56.867Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:42:56.867Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:42:56.867Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:42:56.867Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:42:56.867Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.867Z]   [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.867Z] 
  [CASE START] - TC-3870BD48: should handle unknown event types gracefully
[2025-06-21T21:42:56.867Z]   Module: dialog
[2025-06-21T21:42:56.867Z]   Full Path: Dialog Manager › 状态机服务生命周期管理 › should handle unknown event types gracefully
[2025-06-21T21:42:56.868Z]   [Arrange] - Precondition: Test environment prepared for "should handle unknown event types gracefully"
[2025-06-21T21:42:56.868Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:42:56.868Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:42:56.868Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:42:56.868Z]   [Act] - Step: Executing test logic for "should handle unknown event types gracefully"
[2025-06-21T21:42:56.868Z]   [Act Log] - Loading target module: whatsapp/dialogMachine.js
[2025-06-21T21:42:56.868Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:42:56.868Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:42:56.868Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:42:56.868Z]   [Act Log] - Captured 0 program outputs during execution
[2025-06-21T21:42:56.868Z]   [Act Log] - No program outputs captured (silent execution)
[2025-06-21T21:42:56.868Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:42:56.868Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:42:56.868Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:42:56.868Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:42:56.868Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:42:56.868Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:42:56.868Z]   [CASE END] - Duration: 1ms
[2025-06-21T21:42:56.890Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/whatsapp/dialogMachine.test.js
[2025-06-21T21:42:56.890Z] Suite Duration: 12873ms
[2025-06-21T21:42:56.890Z] Suite Results: 5 passed, 1 failed, 0 skipped
[2025-06-21T21:42:56.895Z] 
================================================================================
[2025-06-21T21:42:56.896Z] Test Run Finished
[2025-06-21T21:42:56.896Z] End Timestamp: 2025-06-21T21:42:56.895Z
[2025-06-21T21:42:56.896Z] Total Duration: 12934ms (12.93s)
[2025-06-21T21:42:56.896Z] 
[STATISTICS]
[2025-06-21T21:42:56.896Z] Total Test Suites: 1
[2025-06-21T21:42:56.896Z] Passed Test Suites: 0
[2025-06-21T21:42:56.896Z] Failed Test Suites: 1
[2025-06-21T21:42:56.896Z] Total Tests: 6
[2025-06-21T21:42:56.896Z] Passed Tests: 5
[2025-06-21T21:42:56.896Z] Failed Tests: 1
[2025-06-21T21:42:56.896Z] Skipped Tests: 0
[2025-06-21T21:42:56.896Z] Success Rate: 83.33%
[2025-06-21T21:42:56.896Z] Overall Result: FAILURE
[2025-06-21T21:42:56.896Z] ================================================================================
