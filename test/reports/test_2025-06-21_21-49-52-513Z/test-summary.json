{"summary": {"totalTests": 1, "passedTests": 1, "failedTests": 0, "skippedTests": 0, "totalDuration": 577, "success": false, "timestamp": "2025-06-21T21:49:53.095Z", "environment": "test"}, "projects": {"UNIT": {"tests": [{"caseId": "U-AUTO-001", "title": "should show console and winston output in logs", "status": "passed", "duration": 91, "failureMessages": [], "ancestorTitles": ["Simple Log Test"], "capturedLogs": 0}], "summary": {"total": 1, "passed": 1, "failed": 0, "skipped": 0, "duration": 91}}}, "coverage": {}, "performance": {}}