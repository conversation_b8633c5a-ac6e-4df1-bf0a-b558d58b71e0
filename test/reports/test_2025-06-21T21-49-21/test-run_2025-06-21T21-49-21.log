[2025-06-21T21:49:21.268Z] ================================================================================
[2025-06-21T21:49:21.268Z] Test Run Started
[2025-06-21T21:49:21.268Z] Timestamp: 2025-06-21T21:49:21.267Z
[2025-06-21T21:49:21.268Z] Operating System: Linux 6.11.0-26-generic x64
[2025-06-21T21:49:21.268Z] Runtime Version: Node.js v18.20.5
[2025-06-21T21:49:21.268Z] Working Directory: /home/<USER>/firespoon/Firespoon_API_TF
[2025-06-21T21:49:21.268Z] ================================================================================
[2025-06-21T21:49:21.272Z] 
[RUN START] - Test execution beginning
[2025-06-21T21:49:21.272Z] Total Test Suites: 1
[2025-06-21T21:49:21.272Z] Test Environment: test
[2025-06-21T21:49:21.281Z] 
[SUITE START] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:49:21.281Z] Suite Display Name: UNIT
[2025-06-21T21:49:21.979Z] 
  [CASE START] - TC-24783ED: should capture console.log output
[2025-06-21T21:49:21.979Z]   Module: log
[2025-06-21T21:49:21.979Z]   Full Path: Log Capture Test › should capture console.log output
[2025-06-21T21:49:21.999Z]   [Arrange] - Precondition: Test environment prepared for "should capture console.log output"
[2025-06-21T21:49:21.999Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:49:21.999Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:49:21.999Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:49:21.999Z]   [Act] - Step: Executing test logic for "should capture console.log output"
[2025-06-21T21:49:21.999Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:49:21.999Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:49:21.999Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:49:21.999Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:49:21.999Z]   [Act Log] - Captured 4 program outputs during execution
[2025-06-21T21:49:21.999Z]   [Act Log] - console: 2 outputs
[2025-06-21T21:49:21.999Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:49:21.999Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:49:21.999Z]   [Act Log] - +26ms [console.log] Console log level: info
[2025-06-21T21:49:21.999Z]   [Act Log] - +26ms [process.stdout] Console log level: info

[2025-06-21T21:49:21.999Z]   [Act Log] - +28ms [console.log] File log level: warn
[2025-06-21T21:49:21.999Z]   [Act Log] - +28ms [process.stdout] File log level: warn

[2025-06-21T21:49:21.999Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:49:21.999Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:49:21.999Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:49:21.999Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:49:21.999Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:49:21.999Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:49:22.000Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.000Z]   [CASE END] - Duration: 48ms
[2025-06-21T21:49:22.000Z] 
  [CASE START] - TC-4ABC9FD2: should capture winston logger output
[2025-06-21T21:49:22.000Z]   Module: log
[2025-06-21T21:49:22.000Z]   Full Path: Log Capture Test › should capture winston logger output
[2025-06-21T21:49:22.020Z]   [Arrange] - Precondition: Test environment prepared for "should capture winston logger output"
[2025-06-21T21:49:22.020Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:49:22.020Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:49:22.020Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:49:22.020Z]   [Act] - Step: Executing test logic for "should capture winston logger output"
[2025-06-21T21:49:22.020Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:49:22.020Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:49:22.020Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:49:22.020Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:49:22.020Z]   [Act Log] - Captured 3 program outputs during execution
[2025-06-21T21:49:22.020Z]   [Act Log] - process: 3 outputs
[2025-06-21T21:49:22.020Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:49:22.020Z]   [Act Log] - +18ms [process.stdout] 2025-06-21 22:49:22:4922 [32minfo[39m: [32mThis is a test winston info message[39m

[2025-06-21T21:49:22.020Z]   [Act Log] - +18ms [process.stdout] 2025-06-21 22:49:22:4922 [33mwarn[39m: [33mThis is a test winston warn message[39m

[2025-06-21T21:49:22.020Z]   [Act Log] - +19ms [process.stdout] 2025-06-21 22:49:22:4922 [31merror[39m: [31mThis is a test winston error message[39m

[2025-06-21T21:49:22.020Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:49:22.020Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:49:22.020Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:49:22.020Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:49:22.020Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:49:22.020Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:49:22.020Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.020Z]   [CASE END] - Duration: 20ms
[2025-06-21T21:49:22.021Z] 
  [CASE START] - TC-65E5315B: should capture mixed console and winston output
[2025-06-21T21:49:22.021Z]   Module: log
[2025-06-21T21:49:22.021Z]   Full Path: Log Capture Test › should capture mixed console and winston output
[2025-06-21T21:49:22.040Z]   [Arrange] - Precondition: Test environment prepared for "should capture mixed console and winston output"
[2025-06-21T21:49:22.040Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:49:22.040Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:49:22.040Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:49:22.040Z]   [Act] - Step: Executing test logic for "should capture mixed console and winston output"
[2025-06-21T21:49:22.040Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:49:22.040Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:49:22.040Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:49:22.040Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:49:22.040Z]   [Act Log] - Captured 2 program outputs during execution
[2025-06-21T21:49:22.040Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:49:22.040Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:49:22.040Z]   [Act Log] - +17ms [process.stdout] 2025-06-21 22:49:22:4922 [32minfo[39m: [32mWinston: Test initialization complete[39m

[2025-06-21T21:49:22.040Z]   [Act Log] - +18ms [process.stdout] 2025-06-21 22:49:22:4922 [32minfo[39m: [32mWinston: Test completed successfully[39m

[2025-06-21T21:49:22.040Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:49:22.040Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:49:22.040Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:49:22.040Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:49:22.040Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:49:22.040Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:49:22.040Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.040Z]   [CASE END] - Duration: 18ms
[2025-06-21T21:49:22.040Z] 
  [CASE START] - TC-8B4356C: should capture error scenarios
[2025-06-21T21:49:22.040Z]   Module: log
[2025-06-21T21:49:22.040Z]   Full Path: Log Capture Test › should capture error scenarios
[2025-06-21T21:49:22.056Z]   [Arrange] - Precondition: Test environment prepared for "should capture error scenarios"
[2025-06-21T21:49:22.056Z]   [Arrange Log] - Setting up test environment variables
[2025-06-21T21:49:22.056Z]   [Arrange Log] - Initializing mock objects and dependencies
[2025-06-21T21:49:22.056Z]   [Arrange Log] - Preparing test data and fixtures
[2025-06-21T21:49:22.056Z]   [Act] - Step: Executing test logic for "should capture error scenarios"
[2025-06-21T21:49:22.056Z]   [Act Log] - Loading target module: test-log-capture.js
[2025-06-21T21:49:22.056Z]   [Act Log] - Instantiating service/class under test
[2025-06-21T21:49:22.056Z]   [Act Log] - Configuring test parameters and inputs
[2025-06-21T21:49:22.056Z]   [Act Log] - Invoking target method or function
[2025-06-21T21:49:22.056Z]   [Act Log] - Captured 2 program outputs during execution
[2025-06-21T21:49:22.056Z]   [Act Log] - process: 2 outputs
[2025-06-21T21:49:22.056Z]   [Act Log] - === Program Output Details ===
[2025-06-21T21:49:22.056Z]   [Act Log] - +15ms [process.stdout] 2025-06-21 22:49:22:4922 [33mwarn[39m: [33mWinston: Warning - risky operation detected[39m

[2025-06-21T21:49:22.056Z]   [Act Log] - +15ms [process.stdout] 2025-06-21 22:49:22:4922 [32minfo[39m: [32mWinston: Operation completed without errors[39m

[2025-06-21T21:49:22.056Z]   [Act Log] - === End Program Output ===
[2025-06-21T21:49:22.056Z]   [Act Log] - Method execution completed successfully
[2025-06-21T21:49:22.057Z]   [Act Log] - No exceptions thrown during execution
[2025-06-21T21:49:22.057Z]   [Act Log] - All function calls returned expected types
[2025-06-21T21:49:22.057Z]   [Act Log] - Memory allocation and cleanup successful
[2025-06-21T21:49:22.057Z]   [Assert] - Verifying: All assertions and expectations
[2025-06-21T21:49:22.057Z]   [RESULT] - PASS: Test completed successfully
[2025-06-21T21:49:22.057Z]   [CASE END] - Duration: 16ms
[2025-06-21T21:49:22.062Z] [SUITE END] - File: /home/<USER>/firespoon/Firespoon_API_TF/test/unit/test-log-capture.test.js
[2025-06-21T21:49:22.062Z] Suite Duration: 757ms
[2025-06-21T21:49:22.062Z] Suite Results: 4 passed, 0 failed, 0 skipped
[2025-06-21T21:49:22.068Z] 
================================================================================
[2025-06-21T21:49:22.068Z] Test Run Finished
[2025-06-21T21:49:22.068Z] End Timestamp: 2025-06-21T21:49:22.068Z
[2025-06-21T21:49:22.068Z] Total Duration: 801ms (0.80s)
[2025-06-21T21:49:22.068Z] 
[STATISTICS]
[2025-06-21T21:49:22.068Z] Total Test Suites: 1
[2025-06-21T21:49:22.068Z] Passed Test Suites: 1
[2025-06-21T21:49:22.068Z] Failed Test Suites: 0
[2025-06-21T21:49:22.068Z] Total Tests: 4
[2025-06-21T21:49:22.068Z] Passed Tests: 4
[2025-06-21T21:49:22.068Z] Failed Tests: 0
[2025-06-21T21:49:22.068Z] Skipped Tests: 0
[2025-06-21T21:49:22.068Z] Success Rate: 100.00%
[2025-06-21T21:49:22.068Z] Overall Result: FAILURE
[2025-06-21T21:49:22.068Z] ================================================================================
